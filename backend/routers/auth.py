from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database import get_db
from models import Admin, Company
from schemas import LoginRequest, LoginResponse
from auth import authenticate_admin, create_access_token
from datetime import timedelta

router = APIRouter()

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    admin = authenticate_admin(db, login_data.username, login_data.password)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Credenciais inválidas",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Buscar dados da empresa
    company = db.query(Company).filter(Company.id == admin.company_id).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Empresa não encontrada"
        )
    
    # Criar token JWT
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(admin.id), "company_id": admin.company_id},
        expires_delta=access_token_expires
    )
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        company={
            "id": company.id,
            "name": company.name
        }
    )