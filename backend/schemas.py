from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal

# Auth Schemas
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    company: dict

# Company Schemas
class CompanyResponse(BaseModel):
    id: int
    name: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Admin Schemas
class AdminResponse(BaseModel):
    id: int
    username: str
    email: Optional[str]
    company_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Team Member Schemas
class TeamMemberCreate(BaseModel):
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None

class TeamMemberUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None

class TeamMemberResponse(BaseModel):
    id: int
    name: str
    email: Optional[str]
    phone: Optional[str]
    position: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Product Schemas
class ProductCreate(BaseModel):
    name: str
    price: Decimal

class ProductUpdate(BaseModel):
    name: Optional[str] = None
    price: Optional[Decimal] = None

class ProductResponse(BaseModel):
    id: int
    name: str
    price: Decimal
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Lead Schemas
class LeadCreate(BaseModel):
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    social_media: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = "new"

class LeadUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    social_media: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None

class LeadResponse(BaseModel):
    id: int
    name: str
    email: Optional[str]
    phone: Optional[str]
    social_media: Optional[str]
    source: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Sale Schemas
class SaleCreate(BaseModel):
    lead_id: Optional[int] = None
    product_id: Optional[int] = None
    amount: Decimal
    sale_date: date
    notes: Optional[str] = None

class SaleUpdate(BaseModel):
    lead_id: Optional[int] = None
    product_id: Optional[int] = None
    amount: Optional[Decimal] = None
    sale_date: Optional[date] = None
    notes: Optional[str] = None

class SaleResponse(BaseModel):
    id: int
    lead_id: Optional[int]
    product_id: Optional[int]
    amount: Decimal
    sale_date: date
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    lead: Optional[LeadResponse] = None
    product: Optional[ProductResponse] = None

    class Config:
        from_attributes = True

# Goal Schemas
class GoalCreate(BaseModel):
    title: str
    description: Optional[str] = None
    target_value: Decimal
    current_value: Optional[Decimal] = 0
    goal_type: str  # 'sales', 'leads', 'revenue'
    period_type: str  # 'monthly', 'quarterly', 'yearly'
    start_date: date
    end_date: date

class GoalUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    target_value: Optional[Decimal] = None
    current_value: Optional[Decimal] = None
    goal_type: Optional[str] = None
    period_type: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None

class GoalResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    target_value: Decimal
    current_value: Decimal
    goal_type: str
    period_type: str
    start_date: date
    end_date: date
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Followup Schemas
class FollowupCreate(BaseModel):
    lead_id: Optional[int] = None
    team_member_id: Optional[int] = None
    title: str
    description: Optional[str] = None
    scheduled_date: datetime
    status: Optional[str] = "pending"
    notes: Optional[str] = None

class FollowupUpdate(BaseModel):
    lead_id: Optional[int] = None
    team_member_id: Optional[int] = None
    title: Optional[str] = None
    description: Optional[str] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class FollowupResponse(BaseModel):
    id: int
    lead_id: Optional[int]
    team_member_id: Optional[int]
    title: str
    description: Optional[str]
    scheduled_date: datetime
    status: str
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    lead: Optional[LeadResponse] = None
    team_member: Optional[TeamMemberResponse] = None

    class Config:
        from_attributes = True

# Preorder Schemas
class PreorderCreate(BaseModel):
    lead_id: Optional[int] = None
    product_id: Optional[int] = None
    amount: Decimal
    status: Optional[str] = "pending"
    notes: Optional[str] = None

class PreorderUpdate(BaseModel):
    lead_id: Optional[int] = None
    product_id: Optional[int] = None
    amount: Optional[Decimal] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class PreorderResponse(BaseModel):
    id: int
    lead_id: Optional[int]
    product_id: Optional[int]
    amount: Decimal
    status: str
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    lead: Optional[LeadResponse] = None
    product: Optional[ProductResponse] = None

    class Config:
        from_attributes = True

# Dashboard Schemas
class DashboardKPIs(BaseModel):
    total_revenue: Decimal
    total_leads: int
    conversion_rate: float
    monthly_sales: Decimal
    goals_achieved: int
    total_goals: int
    pending_followups: int
    total_team_members: int

# Audit Log Schemas
class AuditLogResponse(BaseModel):
    id: int
    action: str
    table_name: str
    record_id: Optional[int]
    old_values: Optional[dict]
    new_values: Optional[dict]
    created_at: datetime
    admin: Optional[AdminResponse] = None

    class Config:
        from_attributes = True