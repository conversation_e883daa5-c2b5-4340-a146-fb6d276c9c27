#!/usr/bin/env python3
"""
Script para popular o banco de dados com dados iniciais
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Base, Company, Admin, TeamMember, Product, Lead, Sale, Goal, Followup, Preorder

# Configuração do banco
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://dev:devpass@localhost:5432/multitenancy_hub")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Contexto para hash de senhas
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def seed_database():
    """Popular o banco com dados iniciais"""
    
    # Criar tabelas
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Verificar se já existem dados
        if db.query(Company).count() > 0:
            print("Banco já possui dados. Pulando seed.")
            return
        
        print("Iniciando seed do banco de dados...")
        
        # 1. Criar empresas
        companies_data = [
            "high_webnar",
            "high_rec", 
            "high_copy",
            "high_capital",
            "high_finance",
            "high_stage",
            "high_pulse",
            "high_agents"
        ]
        
        companies = []
        for company_name in companies_data:
            company = Company(name=company_name)
            db.add(company)
            companies.append(company)
        
        db.commit()
        print(f"✅ Criadas {len(companies)} empresas")
        
        # 2. Criar administradores
        admin_data = [
            ("admin_webnar", "<EMAIL>", 0),
            ("admin_rec", "<EMAIL>", 1),
            ("admin_copy", "<EMAIL>", 2),
            ("admin_capital", "<EMAIL>", 3),
            ("admin_finance", "<EMAIL>", 4),
            ("admin_stage", "<EMAIL>", 5),
            ("admin_pulse", "<EMAIL>", 6),
            ("admin_agents", "<EMAIL>", 7),
        ]
        
        admins = []
        for username, email, company_idx in admin_data:
            admin = Admin(
                company_id=companies[company_idx].id,
                username=username,
                password_hash=get_password_hash("admin123"),
                email=email
            )
            db.add(admin)
            admins.append(admin)
        
        db.commit()
        print(f"✅ Criados {len(admins)} administradores")
        
        # 3. Criar membros da equipe
        team_members_data = [
            ("João Silva", "<EMAIL>", "(11) 99999-1111", "CEO", 0),
            ("Maria Santos", "<EMAIL>", "(11) 99999-2222", "Vendedora", 0),
            ("Pedro Costa", "<EMAIL>", "(11) 99999-3333", "Gerente de RH", 1),
            ("Ana Lima", "<EMAIL>", "(11) 99999-4444", "Recrutadora", 1),
            ("Carlos Oliveira", "<EMAIL>", "(11) 99999-5555", "Copywriter", 2),
            ("Lucia Ferreira", "<EMAIL>", "(11) 99999-6666", "Editora", 2),
            ("Roberto Alves", "<EMAIL>", "(11) 99999-7777", "Consultor", 3),
            ("Fernanda Rocha", "<EMAIL>", "(11) 99999-8888", "Analista", 3),
            ("Marcos Pereira", "<EMAIL>", "(11) 99999-9999", "Consultor Financeiro", 4),
            ("Juliana Souza", "<EMAIL>", "(11) 99999-0000", "Analista", 4),
            ("Rafael Mendes", "<EMAIL>", "(11) 99999-1112", "Coach", 5),
            ("Patricia Gomes", "<EMAIL>", "(11) 99999-2223", "Mentora", 5),
            ("Diego Martins", "<EMAIL>", "(11) 99999-3334", "Especialista em Marketing", 6),
            ("Camila Torres", "<EMAIL>", "(11) 99999-4445", "Social Media", 6),
            ("Bruno Silva", "<EMAIL>", "(11) 99999-5556", "Gerente de Vendas", 7),
            ("Gabriela Costa", "<EMAIL>", "(11) 99999-6667", "Vendedora", 7),
        ]
        
        team_members = []
        for name, email, phone, position, company_idx in team_members_data:
            member = TeamMember(
                company_id=companies[company_idx].id,
                name=name,
                email=email,
                phone=phone,
                position=position
            )
            db.add(member)
            team_members.append(member)
        
        db.commit()
        print(f"✅ Criados {len(team_members)} membros da equipe")
        
        # 4. Criar produtos
        products_data = [
            ("Curso de Webinar", 297.00, 0),
            ("Mentoria Premium", 997.00, 0),
            ("Consultoria de Recrutamento", 1500.00, 1),
            ("Treinamento de RH", 500.00, 1),
            ("Copywriting Avançado", 397.00, 2),
            ("E-mail Marketing", 197.00, 2),
            ("Investimentos Imobiliários", 2000.00, 3),
            ("Consultoria Financeira", 800.00, 3),
            ("Planejamento Financeiro", 600.00, 4),
            ("Análise de Crédito", 400.00, 4),
            ("Desenvolvimento Pessoal", 300.00, 5),
            ("Coaching Executivo", 1200.00, 5),
            ("Marketing Digital", 450.00, 6),
            ("Gestão de Redes Sociais", 250.00, 6),
            ("Vendas Consultivas", 800.00, 7),
            ("Atendimento ao Cliente", 350.00, 7),
        ]
        
        products = []
        for name, price, company_idx in products_data:
            product = Product(
                company_id=companies[company_idx].id,
                name=name,
                price=price
            )
            db.add(product)
            products.append(product)
        
        db.commit()
        print(f"✅ Criados {len(products)} produtos")
        
        # 5. Criar leads
        leads_data = [
            ("Cliente Webnar 1", "<EMAIL>", "(11) 88888-1111", "@cliente1", "Facebook", "new", 0),
            ("Cliente Webnar 2", "<EMAIL>", "(11) 88888-2222", "@cliente2", "Instagram", "contacted", 0),
            ("Cliente Rec 1", "<EMAIL>", "(11) 88888-3333", "@cliente3", "LinkedIn", "new", 1),
            ("Cliente Rec 2", "<EMAIL>", "(11) 88888-4444", "@cliente4", "Indicação", "qualified", 1),
            ("Cliente Copy 1", "<EMAIL>", "(11) 88888-5555", "@cliente5", "Google Ads", "new", 2),
            ("Cliente Copy 2", "<EMAIL>", "(11) 88888-6666", "@cliente6", "YouTube", "contacted", 2),
            ("Cliente Capital 1", "<EMAIL>", "(11) 88888-7777", "@cliente7", "Evento", "qualified", 3),
            ("Cliente Capital 2", "<EMAIL>", "(11) 88888-8888", "@cliente8", "Website", "new", 3),
            ("Cliente Finance 1", "<EMAIL>", "(11) 88888-9999", "@cliente9", "Indicação", "contacted", 4),
            ("Cliente Finance 2", "<EMAIL>", "(11) 88888-0000", "@cliente10", "Facebook", "qualified", 4),
            ("Cliente Stage 1", "<EMAIL>", "(11) 88888-1112", "@cliente11", "Instagram", "new", 5),
            ("Cliente Stage 2", "<EMAIL>", "(11) 88888-2223", "@cliente12", "LinkedIn", "contacted", 5),
            ("Cliente Pulse 1", "<EMAIL>", "(11) 88888-3334", "@cliente13", "Google Ads", "qualified", 6),
            ("Cliente Pulse 2", "<EMAIL>", "(11) 88888-4445", "@cliente14", "YouTube", "new", 6),
            ("Cliente Agents 1", "<EMAIL>", "(11) 88888-5556", "@cliente15", "Evento", "contacted", 7),
            ("Cliente Agents 2", "<EMAIL>", "(11) 88888-6667", "@cliente16", "Website", "qualified", 7),
        ]
        
        leads = []
        for name, email, phone, social_media, source, status, company_idx in leads_data:
            lead = Lead(
                company_id=companies[company_idx].id,
                name=name,
                email=email,
                phone=phone,
                social_media=social_media,
                source=source,
                status=status
            )
            db.add(lead)
            leads.append(lead)
        
        db.commit()
        print(f"✅ Criados {len(leads)} leads")
        
        # 6. Criar vendas
        sales_data = [
            (0, 0, 297.00, "2024-01-15", "Venda realizada via WhatsApp"),
            (1, 1, 997.00, "2024-01-20", "Cliente interessado na mentoria"),
            (2, 2, 1500.00, "2024-01-18", "Consultoria de recrutamento"),
            (3, 3, 500.00, "2024-01-22", "Treinamento de RH"),
            (4, 4, 397.00, "2024-01-16", "Curso de copywriting"),
            (5, 5, 197.00, "2024-01-25", "E-mail marketing"),
            (6, 6, 2000.00, "2024-01-19", "Investimento imobiliário"),
            (7, 7, 800.00, "2024-01-23", "Consultoria financeira"),
            (8, 8, 600.00, "2024-01-17", "Planejamento financeiro"),
            (9, 9, 400.00, "2024-01-24", "Análise de crédito"),
            (10, 10, 300.00, "2024-01-21", "Desenvolvimento pessoal"),
            (11, 11, 1200.00, "2024-01-26", "Coaching executivo"),
            (12, 12, 450.00, "2024-01-14", "Marketing digital"),
            (13, 13, 250.00, "2024-01-27", "Gestão de redes sociais"),
            (14, 14, 800.00, "2024-01-13", "Vendas consultivas"),
            (15, 15, 350.00, "2024-01-28", "Atendimento ao cliente"),
        ]
        
        sales = []
        for lead_idx, product_idx, amount, sale_date, notes in sales_data:
            sale = Sale(
                company_id=leads[lead_idx].company_id,
                lead_id=leads[lead_idx].id,
                product_id=products[product_idx].id,
                amount=amount,
                sale_date=sale_date,
                notes=notes
            )
            db.add(sale)
            sales.append(sale)
        
        db.commit()
        print(f"✅ Criadas {len(sales)} vendas")
        
        # 7. Criar metas
        goals_data = [
            ("Meta de Vendas Mensal", "Atingir R$ 50.000 em vendas no mês", 50000.00, 1294.00, "revenue", "monthly", "2024-01-01", "2024-01-31", 0),
            ("Meta de Leads", "Captar 100 leads no mês", 100.00, 2.00, "leads", "monthly", "2024-01-01", "2024-01-31", 0),
            ("Meta de Consultorias", "Realizar 20 consultorias no mês", 20.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 1),
            ("Meta de Cursos Vendidos", "Vender 50 cursos no mês", 50.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 2),
            ("Meta de Investimentos", "Captar R$ 100.000 em investimentos", 100000.00, 2800.00, "revenue", "monthly", "2024-01-01", "2024-01-31", 3),
            ("Meta de Consultorias Financeiras", "Realizar 15 consultorias", 15.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 4),
            ("Meta de Coaching", "Atender 30 clientes de coaching", 30.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 5),
            ("Meta de Marketing", "Vender 40 pacotes de marketing", 40.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 6),
            ("Meta de Vendas", "Realizar 25 vendas no mês", 25.00, 2.00, "sales", "monthly", "2024-01-01", "2024-01-31", 7),
        ]
        
        goals = []
        for title, description, target_value, current_value, goal_type, period_type, start_date, end_date, company_idx in goals_data:
            goal = Goal(
                company_id=companies[company_idx].id,
                title=title,
                description=description,
                target_value=target_value,
                current_value=current_value,
                goal_type=goal_type,
                period_type=period_type,
                start_date=start_date,
                end_date=end_date
            )
            db.add(goal)
            goals.append(goal)
        
        db.commit()
        print(f"✅ Criadas {len(goals)} metas")
        
        # 8. Criar follow-ups
        followups_data = [
            (0, 1, "Follow-up Cliente Webnar 1", "Ligar para cliente interessado no curso", "2024-01-30 14:00:00", "pending", "Cliente demonstrou interesse"),
            (1, 1, "Reunião Cliente Webnar 2", "Apresentar mentoria premium", "2024-01-31 10:00:00", "pending", "Cliente qualificado"),
            (2, 3, "Follow-up Cliente Rec 1", "Entrevista para recrutamento", "2024-01-29 15:00:00", "pending", "Candidato interessado"),
            (3, 3, "Reunião Cliente Rec 2", "Apresentar treinamento de RH", "2024-01-30 09:00:00", "pending", "Empresa interessada"),
            (4, 5, "Follow-up Cliente Copy 1", "Demonstrar copywriting", "2024-01-28 16:00:00", "pending", "Cliente novo"),
            (5, 5, "Reunião Cliente Copy 2", "Apresentar e-mail marketing", "2024-01-29 11:00:00", "pending", "Cliente contatado"),
            (6, 7, "Follow-up Cliente Capital 1", "Consultoria de investimentos", "2024-01-27 14:00:00", "pending", "Cliente qualificado"),
            (7, 7, "Reunião Cliente Capital 2", "Apresentar consultoria financeira", "2024-01-28 10:00:00", "pending", "Cliente novo"),
            (8, 9, "Follow-up Cliente Finance 1", "Planejamento financeiro", "2024-01-26 15:00:00", "pending", "Cliente contatado"),
            (9, 9, "Reunião Cliente Finance 2", "Análise de crédito", "2024-01-27 09:00:00", "pending", "Cliente qualificado"),
            (10, 11, "Follow-up Cliente Stage 1", "Desenvolvimento pessoal", "2024-01-25 16:00:00", "pending", "Cliente novo"),
            (11, 11, "Reunião Cliente Stage 2", "Coaching executivo", "2024-01-26 11:00:00", "pending", "Cliente contatado"),
            (12, 13, "Follow-up Cliente Pulse 1", "Marketing digital", "2024-01-24 14:00:00", "pending", "Cliente qualificado"),
            (13, 13, "Reunião Cliente Pulse 2", "Gestão de redes sociais", "2024-01-25 10:00:00", "pending", "Cliente novo"),
            (14, 15, "Follow-up Cliente Agents 1", "Vendas consultivas", "2024-01-23 15:00:00", "pending", "Cliente contatado"),
            (15, 15, "Reunião Cliente Agents 2", "Atendimento ao cliente", "2024-01-24 09:00:00", "pending", "Cliente qualificado"),
        ]
        
        followups = []
        for lead_idx, team_member_idx, title, description, scheduled_date, status, notes in followups_data:
            followup = Followup(
                company_id=leads[lead_idx].company_id,
                lead_id=leads[lead_idx].id,
                team_member_id=team_members[team_member_idx].id,
                title=title,
                description=description,
                scheduled_date=scheduled_date,
                status=status,
                notes=notes
            )
            db.add(followup)
            followups.append(followup)
        
        db.commit()
        print(f"✅ Criados {len(followups)} follow-ups")
        
        # 9. Criar pré-vendas
        preorders_data = [
            (0, 0, 297.00, "pending", "Cliente interessado no curso"),
            (1, 1, 997.00, "confirmed", "Pré-venda confirmada"),
            (2, 2, 1500.00, "pending", "Aguardando confirmação"),
            (3, 3, 500.00, "confirmed", "Pré-venda confirmada"),
            (4, 4, 397.00, "pending", "Cliente em análise"),
            (5, 5, 197.00, "confirmed", "Pré-venda confirmada"),
            (6, 6, 2000.00, "pending", "Aguardando documentação"),
            (7, 7, 800.00, "confirmed", "Pré-venda confirmada"),
            (8, 8, 600.00, "pending", "Cliente interessado"),
            (9, 9, 400.00, "confirmed", "Pré-venda confirmada"),
            (10, 10, 300.00, "pending", "Aguardando confirmação"),
            (11, 11, 1200.00, "confirmed", "Pré-venda confirmada"),
            (12, 12, 450.00, "pending", "Cliente em análise"),
            (13, 13, 250.00, "confirmed", "Pré-venda confirmada"),
            (14, 14, 800.00, "pending", "Aguardando confirmação"),
            (15, 15, 350.00, "confirmed", "Pré-venda confirmada"),
        ]
        
        preorders = []
        for lead_idx, product_idx, amount, status, notes in preorders_data:
            preorder = Preorder(
                company_id=leads[lead_idx].company_id,
                lead_id=leads[lead_idx].id,
                product_id=products[product_idx].id,
                amount=amount,
                status=status,
                notes=notes
            )
            db.add(preorder)
            preorders.append(preorder)
        
        db.commit()
        print(f"✅ Criadas {len(preorders)} pré-vendas")
        
        print("\n🎉 Seed do banco de dados concluído com sucesso!")
        print("\n📋 Resumo dos dados criados:")
        print(f"   • {len(companies)} empresas")
        print(f"   • {len(admins)} administradores")
        print(f"   • {len(team_members)} membros da equipe")
        print(f"   • {len(products)} produtos")
        print(f"   • {len(leads)} leads")
        print(f"   • {len(sales)} vendas")
        print(f"   • {len(goals)} metas")
        print(f"   • {len(followups)} follow-ups")
        print(f"   • {len(preorders)} pré-vendas")
        
        print("\n🔐 Credenciais de acesso:")
        for admin in admins:
            company = next(c for c in companies if c.id == admin.company_id)
            print(f"   • {admin.username} ({company.name}) - senha: admin123")
        
    except Exception as e:
        print(f"❌ Erro durante o seed: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()